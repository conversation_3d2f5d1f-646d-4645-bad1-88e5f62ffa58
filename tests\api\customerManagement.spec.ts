import { test, expect, APIRequestContext } from '@playwright/test';

// Customer Management API Tests
interface CustomerData {
  name: string;
  email: string;
  contactNumber: string;
  address: string;
  countryId: number;
  agentId: number;
}

// Environment configuration
const env = {
  urlCustomer: 'https://api-gateway-qa-775123275503.us-central1.run.app/customer-management',
  auth_token: 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6ImZlNjVjY2I4ZWFkMGJhZWY1ZmQzNjE5NWQ2NTI4YTA1NGZiYjc2ZjMiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.RdIcOe-UIv8aJAewQPHOvUija2B5V1YUow5LSmRMZmZQZEVvP1-tC-iepy0u1fbyH7L0iiqgJdv5DVFKjsF1XFPZja3g0DrGP5smKkokai4hVp4pJHaPrlIJdle5GW2JGkGwcb5SfpMmQ4W91fXJRm1vz6LznL5LDGotnR5bJBF_Y_OP0qZxuQMpe9wOec823qHmcq5qexfywgUTT20tyW9cP0O2adLkEGWTRJwpovB2R_-hBmOWMu_rYUClYrNbVY04fA4qh2Yk7N1QlXycxY0Gpcci4tvcpbwKw7EkDpgAKeQTrDk9netoWIRiVlOJERR8asM5O1m3CTYFN8Zz6A',
  agentId: 221,
  responseTime: 3000
};

class CustomerAPIHelper {
  private request: APIRequestContext;
  private createdCustomerId: number | null = null;

  constructor(request: APIRequestContext) {
    this.request = request;
  }

  async createCustomer(customerData: CustomerData): Promise<number> {
    const response = await this.request.post(`${env.urlCustomer}/api/v1/${env.agentId}/customers`, {
      headers: {
        'Authorization': env.auth_token,
        'Content-Type': 'application/json'
      },
      data: customerData
    });

    expect(response.ok()).toBeTruthy();
    const responseBody = await response.json();
    expect(responseBody.success).toBe(true);
    expect(responseBody.data).toHaveProperty('id');
    
    this.createdCustomerId = responseBody.data.id;
    return this.createdCustomerId;
  }

  async getCustomer(customerId: number) {
    const response = await this.request.get(`${env.urlCustomer}/api/v1/${env.agentId}/customers/${customerId}`, {
      headers: {
        'Authorization': env.auth_token
      }
    });

    expect(response.ok()).toBeTruthy();
    return await response.json();
  }

  async searchCustomers(searchParams: { name?: string, email?: string }) {
    const queryParams = new URLSearchParams();
    if (searchParams.name) queryParams.append('name', searchParams.name);
    if (searchParams.email) queryParams.append('email', searchParams.email);

    const response = await this.request.get(`${env.urlCustomer}/api/v1/${env.agentId}/customers/search?${queryParams}`, {
      headers: {
        'Authorization': env.auth_token
      }
    });

    expect(response.ok()).toBeTruthy();
    return await response.json();
  }

  async getAllCustomers(page: number = 0, size: number = 10) {
    const response = await this.request.post(`${env.urlCustomer}/api/v1/${env.agentId}/customers/list`, {
      headers: {
        'Authorization': env.auth_token,
        'Content-Type': 'application/json'
      },
      data: {
        page: page,
        size: size,
        sort: {
          field: 'id',
          direction: 'ASC'
        }
      }
    });

    expect(response.ok()).toBeTruthy();
    return await response.json();
  }

  async updateCustomerStatus(customerId: number, status: 'ACTIVE' | 'INACTIVE') {
    const response = await this.request.patch(`${env.urlCustomer}/api/v1/${env.agentId}/customers/${customerId}/status`, {
      headers: {
        'Authorization': env.auth_token,
        'Content-Type': 'application/json'
      },
      data: {
        status: status
      }
    });

    expect(response.ok()).toBeTruthy();
    return await response.json();
  }

  async linkCustomersInBulk(customerIds: number[]) {
    const response = await this.request.post(`${env.urlCustomer}/api/v1/${env.agentId}/customers/link-customers-in-bulk`, {
      headers: {
        'Authorization': env.auth_token,
        'Content-Type': 'application/json'
      },
      data: {
        customerIds: customerIds
      }
    });

    expect(response.ok()).toBeTruthy();
    return await response.json();
  }

  getCreatedCustomerId(): number | null {
    return this.createdCustomerId;
  }
}

test.describe('Customer Management API Tests', () => {
  let customerHelper: CustomerAPIHelper;
  let testCustomerData: CustomerData;

  test.beforeAll(async ({ request }) => {
    customerHelper = new CustomerAPIHelper(request);
    
    // Generate unique test data
    const timestamp = Date.now();
    testCustomerData = {
      name: `Test Customer ${timestamp}`,
      email: `test.customer.${timestamp}@example.com`,
      contactNumber: '+551130446571',
      address: 'Test Address, Brazil',
      countryId: 30, // Brazil
      agentId: env.agentId
    };
  });

  test('TC-CM-01: Create Customer Successfully', async ({ request }) => {
    const startTime = Date.now();
    
    const customerId = await customerHelper.createCustomer(testCustomerData);
    
    const responseTime = Date.now() - startTime;
    expect(responseTime).toBeLessThan(env.responseTime);
    expect(customerId).toBeGreaterThan(0);
    
    console.log(`✅ Customer created successfully with ID: ${customerId}`);
  });

  test('TC-CM-02: Get Customer by ID', async ({ request }) => {
    const customerId = customerHelper.getCreatedCustomerId();
    expect(customerId).not.toBeNull();

    const startTime = Date.now();
    const customerData = await customerHelper.getCustomer(customerId!);
    const responseTime = Date.now() - startTime;

    expect(responseTime).toBeLessThan(env.responseTime);
    expect(customerData.success).toBe(true);
    expect(customerData.data).toHaveProperty('id', customerId);
    expect(customerData.data).toHaveProperty('name', testCustomerData.name);
    expect(customerData.data).toHaveProperty('email', testCustomerData.email);
    
    console.log(`✅ Customer retrieved successfully: ${customerData.data.name}`);
  });

  test('TC-CM-03: Search Customers by Name', async ({ request }) => {
    const startTime = Date.now();
    const searchResults = await customerHelper.searchCustomers({ name: testCustomerData.name });
    const responseTime = Date.now() - startTime;

    expect(responseTime).toBeLessThan(env.responseTime);
    expect(searchResults.success).toBe(true);
    expect(Array.isArray(searchResults.data)).toBeTruthy();
    
    // Verify our created customer is in the search results
    const foundCustomer = searchResults.data.find((customer: any) => customer.id === customerHelper.getCreatedCustomerId());
    expect(foundCustomer).toBeDefined();
    
    console.log(`✅ Customer search completed. Found ${searchResults.data.length} customers`);
  });

  test('TC-CM-04: Search Customers by Email', async ({ request }) => {
    const startTime = Date.now();
    const searchResults = await customerHelper.searchCustomers({ email: testCustomerData.email });
    const responseTime = Date.now() - startTime;

    expect(responseTime).toBeLessThan(env.responseTime);
    expect(searchResults.success).toBe(true);
    expect(Array.isArray(searchResults.data)).toBeTruthy();
    
    // Verify our created customer is in the search results
    const foundCustomer = searchResults.data.find((customer: any) => customer.email === testCustomerData.email);
    expect(foundCustomer).toBeDefined();
    
    console.log(`✅ Customer email search completed successfully`);
  });

  test('TC-CM-05: Get All Customers with Pagination', async ({ request }) => {
    const startTime = Date.now();
    const customersList = await customerHelper.getAllCustomers(0, 10);
    const responseTime = Date.now() - startTime;

    expect(responseTime).toBeLessThan(env.responseTime);
    expect(customersList.success).toBe(true);
    expect(customersList.data).toHaveProperty('content');
    expect(Array.isArray(customersList.data.content)).toBeTruthy();
    expect(customersList.data).toHaveProperty('totalElements');
    expect(customersList.data).toHaveProperty('totalPages');
    expect(customersList.data).toHaveProperty('size');
    expect(customersList.data).toHaveProperty('number');
    
    console.log(`✅ Retrieved ${customersList.data.content.length} customers from page 0`);
  });

  test('TC-CM-06: Update Customer Status to Inactive', async ({ request }) => {
    const customerId = customerHelper.getCreatedCustomerId();
    expect(customerId).not.toBeNull();

    const startTime = Date.now();
    const updateResult = await customerHelper.updateCustomerStatus(customerId!, 'INACTIVE');
    const responseTime = Date.now() - startTime;

    expect(responseTime).toBeLessThan(env.responseTime);
    expect(updateResult.success).toBe(true);
    
    // Verify the status was updated
    const updatedCustomer = await customerHelper.getCustomer(customerId!);
    expect(updatedCustomer.data.status).toBe('INACTIVE');
    
    console.log(`✅ Customer status updated to INACTIVE successfully`);
  });

  test('TC-CM-07: Update Customer Status to Active', async ({ request }) => {
    const customerId = customerHelper.getCreatedCustomerId();
    expect(customerId).not.toBeNull();

    const startTime = Date.now();
    const updateResult = await customerHelper.updateCustomerStatus(customerId!, 'ACTIVE');
    const responseTime = Date.now() - startTime;

    expect(responseTime).toBeLessThan(env.responseTime);
    expect(updateResult.success).toBe(true);
    
    // Verify the status was updated
    const updatedCustomer = await customerHelper.getCustomer(customerId!);
    expect(updatedCustomer.data.status).toBe('ACTIVE');
    
    console.log(`✅ Customer status updated to ACTIVE successfully`);
  });

  test('TC-CM-08: Link Customers in Bulk', async ({ request }) => {
    const customerId = customerHelper.getCreatedCustomerId();
    expect(customerId).not.toBeNull();

    const startTime = Date.now();
    const linkResult = await customerHelper.linkCustomersInBulk([customerId!]);
    const responseTime = Date.now() - startTime;

    expect(responseTime).toBeLessThan(env.responseTime);
    expect(linkResult.success).toBe(true);
    
    console.log(`✅ Customer linked in bulk successfully`);
  });
});
