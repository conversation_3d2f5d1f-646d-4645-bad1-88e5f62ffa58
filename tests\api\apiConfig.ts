// API Configuration and Environment Management
export interface APIEnvironment {
  // Base URLs
  baseUrl: string;
  personUrl: string;
  urltrip: string;
  urlCustomer: string;
  urltraveler: string;
  transportUrl: string;
  accomodationUrl: string;
  mobilityUrl: string;
  expenseUrl: string;
  urlDocument: string;
  
  // Authentication
  auth_token: string;
  gcpurl: string;
  projectkey: string;
  
  // Zephyr Integration
  projectKey: string;
  zephyrApiToken: string;
  
  // Test Data
  country: string;
  country_id: number;
  dialcode: string;
  email: string;
  password: string;
  agentID: string;
  customerId: number;
  travelerId: number;
  
  // Test Configuration
  responseTime: number;
  
  // Contact Information
  contactNumber: string;
  address: string;
  registrationNumber: string;
}

// Load environment configuration from Postman environment
export const apiEnvironment: APIEnvironment = {
  // Base URLs
  baseUrl: 'http://customer-management-service-dev-775123275503.us-central1.run.app/customer-management/api/v1/100/customers',
  personUrl: 'https://person-management-service-qa-775123275503.us-central1.run.app/person-management',
  urltrip: 'https://api-gateway-qa-775123275503.us-central1.run.app/trip-management',
  urlCustomer: 'https://api-gateway-qa-775123275503.us-central1.run.app/customer-management',
  urltraveler: 'https://traveler-management-service-qa-775123275503.us-central1.run.app/traveler-management',
  transportUrl: 'https://api-gateway-qa-775123275503.us-central1.run.app/transport-management',
  accomodationUrl: 'https://api-gateway-qa-775123275503.us-central1.run.app/accommodation-management',
  mobilityUrl: 'https://api-gateway-qa-775123275503.us-central1.run.app/mobility-management',
  expenseUrl: 'http://expense-management-service-dev-775123275503.us-central1.run.app/expense-management/',
  urlDocument: 'https://api-gateway-qa-775123275503.us-central1.run.app/document-management',
  
  // Authentication
  auth_token: 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6ImZlNjVjY2I4ZWFkMGJhZWY1ZmQzNjE5NWQ2NTI4YTA1NGZiYjc2ZjMiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.RdIcOe-UIv8aJAewQPHOvUija2B5V1YUow5LSmRMZmZQZEVvP1-tC-iepy0u1fbyH7L0iiqgJdv5DVFKjsF1XFPZja3g0DrGP5smKkokai4hVp4pJHaPrlIJdle5GW2JGkGwcb5SfpMmQ4W91fXJRm1vz6LznL5LDGotnR5bJBF_Y_OP0qZxuQMpe9wOec823qHmcq5qexfywgUTT20tyW9cP0O2adLkEGWTRJwpovB2R_-hBmOWMu_rYUClYrNbVY04fA4qh2Yk7N1QlXycxY0Gpcci4tvcpbwKw7EkDpgAKeQTrDk9netoWIRiVlOJERR8asM5O1m3CTYFN8Zz6A',
  gcpurl: 'https://identitytoolkit.googleapis.com',
  projectkey: 'AIzaSyDK23bdi4gOY3ncpUH-TfZm3PwuBW_DEAE',
  
  // Zephyr Integration
  projectKey: 'IHTC',
  zephyrApiToken: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.6kJDrzwz0i26P_odWYc_xx7dJ-4JJp7FpPXtz7Uuuu4',
  
  // Test Data
  country: 'Brazil',
  country_id: 30,
  dialcode: '+55',
  email: '<EMAIL>',
  password: 'Admin123!',
  agentID: '221',
  customerId: 651,
  travelerId: 464,
  
  // Test Configuration
  responseTime: 3000,
  
  // Contact Information
  contactNumber: '+************',
  address: 'block25',
  registrationNumber: '15.100.842/6547-05'
};

// API Response Interfaces
export interface APIResponse<T = any> {
  success: boolean;
  message: string;
  status?: number;
  data: T;
}

export interface PaginatedResponse<T = any> {
  content: T[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
}

// Test Case Mapping for Zephyr
export const testCaseMapping = {
  // Agency Management
  'GET_COUNTRIES': 'IHTC-T1173',
  'REGEX_REGISTRATION': 'IHTC-T1174',
  'CREATE_AGENCY': 'IHTC-T1175',
  'GCP_LOGIN': 'IHTC-T1176',
  'RESEND_OTP': 'IHTC-T1177',
  'VERIFY_OTP': 'IHTC-T1178',
  'GET_AGENCY_DETAIL': 'IHTC-T1179',
  'TERMS_CONDITION': 'IHTC-T1180',
  
  // Agent Management
  'REGISTER_AGENT': 'IHTC-T1181',
  'SET_PASSWORD': 'IHTC-T1182',
  'AGENT_STATUS': 'IHTC-T1183',
  'UPDATE_AGENCY': 'IHTC-T1184',
  'CHECK_USER': 'IHTC-T1185',
  'CHECK_USER_EMAIL': 'IHTC-T1186',
  'VIEW_AGENTS_LIST': 'IHTC-T1187',
  'VIEW_AGENT_BY_ID': 'IHTC-T1188',
  
  // Customer Management
  'CREATE_CUSTOMER': 'IHTC-T1189',
  'GET_CUSTOMER': 'IHTC-T1190',
  'SEARCH_CUSTOMERS_NAME': 'IHTC-T1191',
  'GET_ALL_CUSTOMERS': 'IHTC-T1192',
  'GET_CUSTOMER_BY_ID': 'IHTC-T1193',
  'GET_CUSTOMERS_BY_AGENT': 'IHTC-T1194',
  'GET_CUSTOMER_BY_EMAIL': 'IHTC-T1195',
  'SET_CUSTOMER_INACTIVE': 'IHTC-T1196',
  'SET_CUSTOMER_ACTIVE': 'IHTC-T1197',
  'LINK_CUSTOMERS_BULK': 'IHTC-T1198',
  'DELINK_CUSTOMERS_BULK': 'IHTC-T1199',
  
  // Traveler Management
  'ADD_TRAVELER': 'IHTC-T1200',
  'FETCH_TRAVELER_NAME': 'IHTC-T1201',
  'TRAVELER_SEARCH': 'IHTC-T1202',
  'UPDATE_TRAVELER': 'IHTC-T1203',
  'UPDATE_TRAVELER_ALERT': 'IHTC-T1204',
  'CHECK_TRAVELER_BY_ID': 'IHTC-T1205',
  'FETCH_TRAVELER_BY_ID': 'IHTC-T1206',
  'FETCH_TRAVELERS': 'IHTC-T1207',
  'UPDATE_TRAVELER_INACTIVE': 'IHTC-T1208',
  'UPDATE_TRAVELER_ACTIVE': 'IHTC-T1209',
  'UPDATE_TRAVELER_LEAD': 'IHTC-T1210',
  
  // Traveler Relationship
  'ADD_RELATED_TRAVELER': 'IHTC-T1211',
  'ADD_TRAVELER_RELATIONSHIP': 'IHTC-T1212',
  'SEARCH_TRAVELER_RELATIONSHIP': 'IHTC-T1213',
  'UPDATE_TRAVELER_RELATIONSHIP': 'IHTC-T1214',
  'DELETE_TRAVELER_RELATIONSHIP': 'IHTC-T1215',
  
  // Traveler Visa
  'ADD_VISA': 'IHTC-T1216',
  'UPDATE_VISA': 'IHTC-T1217',
  'GET_TRAVELER_VISA': 'IHTC-T1218',
  'DELETE_VISA': 'IHTC-T1219',
  
  // Traveler Passport
  'ADD_PASSPORT': 'IHTC-T1220',
  'UPDATE_PASSPORT': 'IHTC-T1221',
  'VIEW_PASSPORT': 'IHTC-T1222',
  'DELETE_PASSPORT': 'IHTC-T1223',
  
  // Traveler Preferences
  'ADD_TRAVELER_PREFERENCES': 'IHTC-T1224',
  'UPDATE_TRAVELER_PREFERENCES': 'IHTC-T1225',
  'GET_ALL_TRAVELER_PREFERENCES': 'IHTC-T1226',
  'DELETE_TRAVELER_PREFERENCES': 'IHTC-T1227',
  
  // Traveler ID
  'ADD_ID_CARD': 'IHTC-T1228',
  'GET_TRAVELER_ID_CARDS': 'IHTC-T1229',
  'UPDATE_ID_INFO': 'IHTC-T1230',
  'DELETE_ID_INFO': 'IHTC-T1231',
  
  // Trip Management
  'CREATE_TRIP': 'IHTC-T1232',
  'TRIP_LIST': 'IHTC-T1233',
  'TRIP_DATES': 'IHTC-T1234',
  'ADD_FEEDBACK': 'IHTC-T1235',
  'FEEDBACK_LIST': 'IHTC-T1236',
  'FEEDBACK_DETAILS': 'IHTC-T1237',
  'UPDATE_FEEDBACK': 'IHTC-T1238',
  'FEEDBACK_CATEGORY': 'IHTC-T1239',
  'CUSTOMER_LIST': 'IHTC-T1240',
  'ADD_TRAVELER_TO_TRIP': 'IHTC-T1241',
  'GET_TRAVELER_BY_TRIP': 'IHTC-T1242',
  'PAST_TRIPS': 'IHTC-T1243',
  
  // Accommodation
  'ADD_ACCOMMODATION': 'IHTC-T1244',
  'GET_ACCOMMODATIONS': 'IHTC-T1245',
  'UPDATE_ACCOMMODATION': 'IHTC-T1246',
  'GET_ACCOMMODATION_BY_ID': 'IHTC-T1247',
  'DELETE_ACCOMMODATION': 'IHTC-T1248',
  
  // Transportation
  'ADD_TRANSPORT': 'IHTC-T1249',
  'UPDATE_TRANSPORT': 'IHTC-T1250',
  'GET_TRANSPORT_BY_ID': 'IHTC-T1251',
  'DELETE_TRANSPORT': 'IHTC-T1252',
  
  // Mobility
  'PARSE_TRIP_DOCUMENT': 'IHTC-T1253',
  'ADD_MOBILITY': 'IHTC-T1254',
  'UPDATE_MOBILITY': 'IHTC-T1255',
  'GET_MOBILITY_BY_ID': 'IHTC-T1256',
  'GET_ALL_MOBILITIES': 'IHTC-T1257',
  'DELETE_MOBILITY': 'IHTC-T1258',
  
  // Flight Management
  'ADD_FLIGHT': 'IHTC-T1259',
  'UPDATE_FLIGHT': 'IHTC-T1260',
  'GET_FLIGHT_DETAILS': 'IHTC-T1261',
  'GET_ALL_FLIGHTS': 'IHTC-T1262',
  'DELETE_FLIGHT': 'IHTC-T1263',
  'GET_FLIGHT_LEG_DETAILS': 'IHTC-T1264',
  'REMOVE_TRAVELER_FROM_TRIP': 'IHTC-T1265'
};

// Utility functions
export class APIUtils {
  static generateUniqueEmail(prefix: string = 'test'): string {
    const timestamp = Date.now();
    return `${prefix}.${timestamp}@example.com`;
  }

  static generateUniqueString(prefix: string = 'test'): string {
    const timestamp = Date.now();
    return `${prefix}_${timestamp}`;
  }

  static generateRandomPhoneNumber(countryCode: string = '+55'): string {
    const randomNumber = Math.floor(Math.random() * 1000000000).toString().padStart(9, '0');
    return `${countryCode}11${randomNumber}`;
  }

  static formatDate(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  static addDays(date: Date, days: number): Date {
    const result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
  }

  static validateResponseTime(responseTime: number, maxTime: number = apiEnvironment.responseTime): boolean {
    return responseTime < maxTime;
  }

  static validateStatusCode(actual: number, expected: number = 200): boolean {
    return actual === expected;
  }

  static validateContentType(contentType: string, expected: string = 'application/json'): boolean {
    return contentType.includes(expected);
  }
}

export default apiEnvironment;
