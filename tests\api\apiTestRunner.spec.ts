import { test, expect, APIRequestContext } from '@playwright/test';
import { apiEnvironment, testCaseMapping, APIUtils } from './apiConfig';

// Comprehensive API Test Runner with Zephyr Integration
class APITestRunner {
  private request: APIRequestContext;
  private testCycleKey: string = '';
  private testResults: Array<{
    testCaseKey: string;
    testName: string;
    status: 'Pass' | 'Fail';
    comment: string;
    responseTime: number;
    timestamp: string;
  }> = [];

  constructor(request: APIRequestContext) {
    this.request = request;
  }

  // Create Zephyr Test Cycle
  async createTestCycle(): Promise<string> {
    const currentDateTime = new Date().toISOString();
    
    try {
      const response = await this.request.post('https://api.zephyrscale.smartbear.com/v2/testcycles', {
        headers: {
          'Authorization': `Bearer ${apiEnvironment.zephyrApiToken}`,
          'Content-Type': 'application/json'
        },
        data: {
          projectKey: apiEnvironment.projectKey,
          name: `API Automation TestRun - ${currentDateTime}`,
          description: 'Automated API test cycle for ItinereHub APIs',
          status: 'In Progress'
        }
      });

      if (response.ok()) {
        const responseData = await response.json();
        this.testCycleKey = responseData.key;
        console.log(`✅ Created test cycle: ${this.testCycleKey}`);
        return this.testCycleKey;
      } else {
        console.log(`❌ Failed to create test cycle: ${response.status()}`);
        return '';
      }
    } catch (error) {
      console.log(`❌ Error creating test cycle: ${error instanceof Error ? error.message : String(error)}`);
      return '';
    }
  }

  // Execute API test with comprehensive validation
  async executeAPITest(config: {
    testName: string;
    testCaseKey: string;
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
    url: string;
    headers?: Record<string, string>;
    data?: any;
    expectedStatus?: number;
    customValidations?: Array<(response: any, responseBody: any) => void>;
  }): Promise<void> {
    const startTime = Date.now();
    let testStatus: 'Pass' | 'Fail' = 'Pass';
    let failureReasons: string[] = [];

    try {
      console.log(`🧪 Executing: ${config.testName}`);

      // Default headers
      const defaultHeaders = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': apiEnvironment.auth_token,
        ...(config.headers || {})
      };

      // Make API request
      const response = await this.request.fetch(config.url, {
        method: config.method,
        headers: defaultHeaders,
        data: config.data ? JSON.stringify(config.data) : undefined
      });

      const responseTime = Date.now() - startTime;
      let responseBody: any = {};

      try {
        responseBody = await response.json();
      } catch (e) {
        responseBody = await response.text();
      }

      // Basic validations
      try {
        // Status code validation
        const expectedStatus = config.expectedStatus || 200;
        if (response.status() !== expectedStatus) {
          failureReasons.push(`Expected status ${expectedStatus}, got ${response.status()}`);
        }
        
        // Response time validation
        if (!APIUtils.validateResponseTime(responseTime)) {
          failureReasons.push(`Response time ${responseTime}ms exceeds limit ${apiEnvironment.responseTime}ms`);
        }
        
        // Content-Type validation
        const contentType = response.headers()['content-type'];
        if (contentType && !APIUtils.validateContentType(contentType)) {
          failureReasons.push(`Invalid content type: ${contentType}`);
        }

        // Custom validations
        if (config.customValidations) {
          config.customValidations.forEach((validation, index) => {
            try {
              validation(response, responseBody);
            } catch (error) {
              failureReasons.push(`Custom validation ${index + 1} failed: ${error.message}`);
            }
          });
        }

        if (failureReasons.length > 0) {
          testStatus = 'Fail';
        }

      } catch (error: unknown) {
        testStatus = 'Fail';
        failureReasons.push(`Validation error: ${error instanceof Error ? error.message : String(error)}`);
      }

    } catch (error: unknown) {
      testStatus = 'Fail';
      failureReasons.push(`Request failed: ${error instanceof Error ? error.message : String(error)}`);
    }

    const responseTime = Date.now() - startTime;
    
    // Store test result
    this.testResults.push({
      testCaseKey: config.testCaseKey,
      testName: config.testName,
      status: testStatus,
      comment: failureReasons.length > 0 ? failureReasons.join('; ') : 'All validations passed',
      responseTime: responseTime,
      timestamp: new Date().toISOString()
    });

    // Log result
    if (testStatus === 'Pass') {
      console.log(`✅ ${config.testName}: PASSED (${responseTime}ms)`);
    } else {
      console.log(`❌ ${config.testName}: FAILED - ${failureReasons.join('; ')}`);
    }
  }

  // Report results to Zephyr Scale
  async reportToZephyr(): Promise<void> {
    if (!this.testCycleKey) {
      console.log('❌ No test cycle created, skipping Zephyr reporting');
      return;
    }

    console.log(`📊 Reporting ${this.testResults.length} test results to Zephyr...`);

    for (const result of this.testResults) {
      try {
        const response = await this.request.post('https://api.zephyrscale.smartbear.com/v2/testexecutions', {
          headers: {
            'Authorization': `Bearer ${apiEnvironment.zephyrApiToken}`,
            'Content-Type': 'application/json'
          },
          data: {
            projectKey: apiEnvironment.projectKey,
            testCycleKey: this.testCycleKey,
            testCaseKey: result.testCaseKey,
            statusName: result.status,
            comment: `${result.comment} | Response Time: ${result.responseTime}ms | Executed: ${result.timestamp}`
          }
        });

        if (response.ok()) {
          console.log(`✅ Reported ${result.testCaseKey}: ${result.status}`);
        } else {
          console.log(`❌ Failed to report ${result.testCaseKey}: ${response.status()}`);
        }
      } catch (error) {
        console.log(`❌ Error reporting ${result.testCaseKey}: ${error.message}`);
      }
    }
  }

  // Generate test summary
  generateSummary(): void {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.status === 'Pass').length;
    const failedTests = totalTests - passedTests;
    const averageResponseTime = this.testResults.reduce((sum, r) => sum + r.responseTime, 0) / totalTests;

    console.log('\n📈 TEST EXECUTION SUMMARY');
    console.log('========================');
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests} (${((passedTests / totalTests) * 100).toFixed(1)}%)`);
    console.log(`Failed: ${failedTests} (${((failedTests / totalTests) * 100).toFixed(1)}%)`);
    console.log(`Average Response Time: ${averageResponseTime.toFixed(0)}ms`);
    console.log(`Test Cycle: ${this.testCycleKey}`);
    console.log('========================\n');

    if (failedTests > 0) {
      console.log('❌ FAILED TESTS:');
      this.testResults
        .filter(r => r.status === 'Fail')
        .forEach(r => {
          console.log(`   ${r.testCaseKey}: ${r.testName} - ${r.comment}`);
        });
      console.log('');
    }
  }

  getTestResults() {
    return this.testResults;
  }
}

// Main Test Suite
test.describe('ItinereHub API Automation - Complete Test Suite', () => {
  let apiRunner: APITestRunner;

  test.beforeAll(async ({ request }) => {
    apiRunner = new APITestRunner(request);
    await apiRunner.createTestCycle();
  });

  test.afterAll(async () => {
    apiRunner.generateSummary();
    await apiRunner.reportToZephyr();
  });

  // Agency Management Tests
  test('Agency Management - Get Countries List', async () => {
    await apiRunner.executeAPITest({
      testName: 'Get Countries List',
      testCaseKey: testCaseMapping.GET_COUNTRIES,
      method: 'GET',
      url: `${apiEnvironment.personUrl}/api/v1/housekeeping/countries`,
      headers: { 'Accept': 'application/json' },
      customValidations: [
        (_response, body) => {
          expect(body).toHaveProperty('success', true);
          expect(body).toHaveProperty('message', 'All countries fetched successfully');
          expect(body).toHaveProperty('data');
          expect(Array.isArray(body.data)).toBeTruthy();
          expect(body.data.length).toBeGreaterThan(0);

          // Validate first country structure
          const firstCountry = body.data[0];
          expect(firstCountry).toHaveProperty('id');
          expect(firstCountry).toHaveProperty('name');
          expect(firstCountry).toHaveProperty('isoAlpha2');
          expect(firstCountry).toHaveProperty('isoAlpha3');
        }
      ]
    });
  });

  test('Agency Management - Registration Number Regex', async () => {
    await apiRunner.executeAPITest({
      testName: 'Get Registration Number Regex',
      testCaseKey: testCaseMapping.REGEX_REGISTRATION,
      method: 'GET',
      url: `${apiEnvironment.personUrl}/api/v1/housekeeping/registration-number-regex`,
      customValidations: [
        (_response, body) => {
          expect(body).toHaveProperty('success', true);
          expect(body).toHaveProperty('data');
        }
      ]
    });
  });

  test('Agency Management - Create Agency', async () => {
    const agencyData = {
      name: APIUtils.generateUniqueString('Test Agency'),
      email: APIUtils.generateUniqueEmail('agency'),
      contactNumber: APIUtils.generateRandomPhoneNumber(),
      address: apiEnvironment.address,
      countryId: apiEnvironment.country_id,
      registrationNumber: apiEnvironment.registrationNumber
    };

    await apiRunner.executeAPITest({
      testName: 'Create Agency',
      testCaseKey: testCaseMapping.CREATE_AGENCY,
      method: 'POST',
      url: `${apiEnvironment.personUrl}/api/v1/agencies`,
      data: agencyData,
      expectedStatus: 201,
      customValidations: [
        (_response, body) => {
          expect(body).toHaveProperty('success', true);
          expect(body).toHaveProperty('data');
          expect(body.data).toHaveProperty('id');
          expect(typeof body.data.id).toBe('number');
        }
      ]
    });
  });

  test('Agency Management - Terms and Conditions', async () => {
    await apiRunner.executeAPITest({
      testName: 'Get Terms and Conditions',
      testCaseKey: testCaseMapping.TERMS_CONDITION,
      method: 'GET',
      url: `${apiEnvironment.personUrl}/api/v1/housekeeping/terms-and-conditions?type=signup&locale=en`,
      customValidations: [
        (_response, body) => {
          expect(body).toHaveProperty('success', true);
          expect(body).toHaveProperty('data');
        }
      ]
    });
  });

  // Customer Management Tests
  test('Customer Management - Create Customer', async () => {
    const customerData = {
      name: APIUtils.generateUniqueString('Test Customer'),
      email: APIUtils.generateUniqueEmail('customer'),
      contactNumber: APIUtils.generateRandomPhoneNumber(),
      address: apiEnvironment.address,
      countryId: apiEnvironment.country_id
    };

    await apiRunner.executeAPITest({
      testName: 'Create Customer',
      testCaseKey: testCaseMapping.CREATE_CUSTOMER,
      method: 'POST',
      url: `${apiEnvironment.urlCustomer}/api/v1/${apiEnvironment.agentID}/customers`,
      data: customerData,
      expectedStatus: 201,
      customValidations: [
        (_response, body) => {
          expect(body).toHaveProperty('success', true);
          expect(body).toHaveProperty('data');
          expect(body.data).toHaveProperty('id');
        }
      ]
    });
  });

  test('Customer Management - Get All Customers', async () => {
    await apiRunner.executeAPITest({
      testName: 'Get All Customers',
      testCaseKey: testCaseMapping.GET_ALL_CUSTOMERS,
      method: 'POST',
      url: `${apiEnvironment.urlCustomer}/api/v1/${apiEnvironment.agentID}/customers/list`,
      data: {
        page: 0,
        size: 10,
        sort: { field: 'id', direction: 'ASC' }
      },
      customValidations: [
        (_response, body) => {
          expect(body).toHaveProperty('success', true);
          expect(body.data).toHaveProperty('content');
          expect(Array.isArray(body.data.content)).toBeTruthy();
          expect(body.data).toHaveProperty('totalElements');
          expect(body.data).toHaveProperty('totalPages');
        }
      ]
    });
  });

  // Traveler Management Tests
  test('Traveler Management - Add Traveler', async () => {
    const travelerData = {
      firstName: APIUtils.generateUniqueString('TestFirst'),
      lastName: APIUtils.generateUniqueString('TestLast'),
      email: APIUtils.generateUniqueEmail('traveler'),
      contactNumber: APIUtils.generateRandomPhoneNumber(),
      dateOfBirth: '1990-01-01',
      gender: 'MALE',
      countryId: apiEnvironment.country_id,
      customerId: apiEnvironment.customerId
    };

    await apiRunner.executeAPITest({
      testName: 'Add Traveler',
      testCaseKey: testCaseMapping.ADD_TRAVELER,
      method: 'POST',
      url: `${apiEnvironment.urltraveler}/api/v1/travelers`,
      data: travelerData,
      expectedStatus: 201,
      customValidations: [
        (_response, body) => {
          expect(body).toHaveProperty('success', true);
          expect(body).toHaveProperty('data');
          expect(body.data).toHaveProperty('id');
        }
      ]
    });
  });
});
