import { test, expect, APIRequestContext } from '@playwright/test';

// Environment configuration from Postman environment
interface EnvironmentConfig {
  baseUrl: string;
  personUrl: string;
  urltrip: string;
  urlCustomer: string;
  urltraveler: string;
  transportUrl: string;
  accomodationUrl: string;
  mobilityUrl: string;
  auth_token: string;
  projectKey: string;
  zephyrApiToken: string;
  country: string;
  email: string;
  password: string;
  agentID: string;
  responseTime: number;
}

// Load environment configuration
const env: EnvironmentConfig = {
  baseUrl: 'http://customer-management-service-dev-775123275503.us-central1.run.app/customer-management/api/v1/100/customers',
  personUrl: 'https://person-management-service-qa-775123275503.us-central1.run.app/person-management',
  urltrip: 'https://api-gateway-qa-775123275503.us-central1.run.app/trip-management',
  urlCustomer: 'https://api-gateway-qa-775123275503.us-central1.run.app/customer-management',
  urltraveler: 'https://traveler-management-service-qa-775123275503.us-central1.run.app/traveler-management',
  transportUrl: 'https://api-gateway-qa-775123275503.us-central1.run.app/transport-management',
  accomodationUrl: 'https://api-gateway-qa-775123275503.us-central1.run.app/accommodation-management',
  mobilityUrl: 'https://api-gateway-qa-775123275503.us-central1.run.app/mobility-management',
  auth_token: 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6ImZlNjVjY2I4ZWFkMGJhZWY1ZmQzNjE5NWQ2NTI4YTA1NGZiYjc2ZjMiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.RdIcOe-UIv8aJAewQPHOvUija2B5V1YUow5LSmRMZmZQZEVvP1-tC-iepy0u1fbyH7L0iiqgJdv5DVFKjsF1XFPZja3g0DrGP5smKkokai4hVp4pJHaPrlIJdle5GW2JGkGwcb5SfpMmQ4W91fXJRm1vz6LznL5LDGotnR5bJBF_Y_OP0qZxuQMpe9wOec823qHmcq5qexfywgUTT20tyW9cP0O2adLkEGWTRJwpovB2R_-hBmOWMu_rYUClYrNbVY04fA4qh2Yk7N1QlXycxY0Gpcci4tvcpbwKw7EkDpgAKeQTrDk9netoWIRiVlOJERR8asM5O1m3CTYFN8Zz6A',
  projectKey: 'IHTC',
  zephyrApiToken: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.6kJDrzwz0i26P_odWYc_xx7dJ-4JJp7FpPXtz7Uuuu4',
  country: 'Brazil',
  email: '<EMAIL>',
  password: 'Admin123!',
  agentID: '221',
  responseTime: 3000
};

// API Test Helper Class
class APITestHelper {
  private request: APIRequestContext;
  private testCycleKey: string = '';
  private testResults: Array<{testCaseKey: string, status: string, comment: string}> = [];

  constructor(request: APIRequestContext) {
    this.request = request;
  }

  // Create Zephyr Test Cycle
  async createTestCycle(): Promise<string> {
    const currentDateTime = new Date().toISOString();
    
    const response = await this.request.post('https://api.zephyrscale.smartbear.com/v2/testcycles', {
      headers: {
        'Authorization': `Bearer ${env.zephyrApiToken}`,
        'Content-Type': 'application/json'
      },
      data: {
        projectKey: env.projectKey,
        name: `API Automation TestRun - ${currentDateTime}`,
        description: 'Automated API test cycle',
        status: 'In Progress'
      }
    });

    expect(response.ok()).toBeTruthy();
    const responseData = await response.json();
    this.testCycleKey = responseData.key;
    console.log(`Created test cycle: ${this.testCycleKey}`);
    return this.testCycleKey;
  }

  // Generic API test method with comprehensive validations
  async testAPI(config: {
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
    url: string,
    headers?: Record<string, string>,
    data?: any,
    testCaseKey: string,
    expectedStatus?: number,
    validations?: Array<(response: any, responseBody: any) => void>
  }) {
    const startTime = Date.now();
    let testStatus = 'Pass';
    let failureReasons: string[] = [];

    try {
      // Default headers
      const defaultHeaders = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...(config.headers || {})
      };

      // Make API request
      const response = await this.request.fetch(config.url, {
        method: config.method,
        headers: defaultHeaders,
        data: config.data ? JSON.stringify(config.data) : undefined
      });

      const responseTime = Date.now() - startTime;
      const responseBody = await response.json().catch(() => ({}));

      // Basic validations
      try {
        // Status code validation
        const expectedStatus = config.expectedStatus || 200;
        expect(response.status()).toBe(expectedStatus);
        
        // Response time validation
        expect(responseTime).toBeLessThan(env.responseTime);
        
        // Content-Type validation
        const contentType = response.headers()['content-type'];
        if (contentType) {
          expect(contentType).toContain('application/json');
        }

        // Custom validations
        if (config.validations) {
          config.validations.forEach(validation => {
            validation(response, responseBody);
          });
        }

        console.log(`✅ ${config.testCaseKey}: API test passed (${responseTime}ms)`);
        
      } catch (error) {
        testStatus = 'Fail';
        const errorMessage = error instanceof Error ? error.message : String(error);
        failureReasons.push(errorMessage);
        console.log(`❌ ${config.testCaseKey}: ${errorMessage}`);
      }

    } catch (error) {
      testStatus = 'Fail';
      const errorMessage = error instanceof Error ? error.message : String(error);
      failureReasons.push(`Request failed: ${errorMessage}`);
      console.log(`❌ ${config.testCaseKey}: Request failed - ${errorMessage}`);
    }

    // Store test result for Zephyr reporting
    this.testResults.push({
      testCaseKey: config.testCaseKey,
      status: testStatus,
      comment: failureReasons.length > 0 ? failureReasons.join(', ') : 'All tests passed'
    });
  }

  // Report results to Zephyr Scale
  async reportToZephyr() {
    for (const result of this.testResults) {
      try {
        const response = await this.request.post('https://api.zephyrscale.smartbear.com/v2/testexecutions', {
          headers: {
            'Authorization': `Bearer ${env.zephyrApiToken}`,
            'Content-Type': 'application/json'
          },
          data: {
            projectKey: env.projectKey,
            testCycleKey: this.testCycleKey,
            testCaseKey: result.testCaseKey,
            statusName: result.status,
            comment: result.comment
          }
        });

        if (response.ok()) {
          console.log(`✅ Reported ${result.testCaseKey} to Zephyr: ${result.status}`);
        } else {
          console.log(`❌ Failed to report ${result.testCaseKey} to Zephyr`);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.log(`❌ Error reporting ${result.testCaseKey}: ${errorMessage}`);
      }
    }
  }
}

// Test Suite
test.describe('ItinereHub API Automation Suite', () => {
  let apiHelper: APITestHelper;

  test.beforeAll(async ({ request }) => {
    apiHelper = new APITestHelper(request);
    await apiHelper.createTestCycle();
  });

  test.afterAll(async () => {
    await apiHelper.reportToZephyr();
  });

  test('TC-01: Get Countries List API', async () => {
    await apiHelper.testAPI({
      method: 'GET',
      url: `${env.personUrl}/api/v1/housekeeping/countries`,
      headers: {
        'Accept': 'application/json'
      },
      testCaseKey: 'IHTC-T1173',
      validations: [
        (_response, body) => {
          // Validate JSON structure
          expect(body).toHaveProperty('success', true);
          expect(body).toHaveProperty('message', 'All countries fetched successfully');
          expect(body).toHaveProperty('status', 0);
          expect(body).toHaveProperty('data');
          expect(Array.isArray(body.data)).toBeTruthy();
          expect(body.data.length).toBeGreaterThan(0);
          
          // Validate country data structure
          const firstCountry = body.data[0];
          const requiredFields = ['id', 'name', 'isoAlpha2', 'isoAlpha3', 'isoNumeric', 
                                 'currencyCode', 'currencyName', 'currencySymbol', 'flag', 'phoneCountryCode'];
          requiredFields.forEach(field => {
            expect(firstCountry).toHaveProperty(field);
          });
          
          // Validate data formats
          expect(typeof firstCountry.id).toBe('number');
          expect(firstCountry.id).toBeGreaterThan(0);
          if (firstCountry.isoAlpha2) expect(firstCountry.isoAlpha2).toMatch(/^[A-Z]{2}$/);
          if (firstCountry.isoAlpha3) expect(firstCountry.isoAlpha3).toMatch(/^[A-Z]{3}$/);
        }
      ]
    });
  });

  test('TC-02: Agency Registration Number Validation', async () => {
    await apiHelper.testAPI({
      method: 'GET',
      url: `${env.personUrl}/api/v1/housekeeping/registration-number-regex`,
      testCaseKey: 'IHTC-T1174',
      validations: [
        (_response, body) => {
          expect(body).toHaveProperty('success', true);
          expect(body).toHaveProperty('data');
          expect(typeof body.data).toBe('object');
        }
      ]
    });
  });

  test('TC-03: Create Agency', async () => {
    const agencyData = {
      name: `Test Agency ${Date.now()}`,
      email: `test.agency.${Date.now()}@example.com`,
      contactNumber: '+551130446571',
      address: 'Test Address',
      countryId: 30, // Brazil
      registrationNumber: '15.100.842/6547-05'
    };

    await apiHelper.testAPI({
      method: 'POST',
      url: `${env.personUrl}/api/v1/agencies`,
      data: agencyData,
      testCaseKey: 'IHTC-T1175',
      expectedStatus: 201,
      validations: [
        (response, body) => {
          expect(body).toHaveProperty('success', true);
          expect(body).toHaveProperty('data');
          expect(body.data).toHaveProperty('id');
          expect(typeof body.data.id).toBe('number');
        }
      ]
    });
  });
});
