# ItinereHub API Automation Framework

This comprehensive API automation framework is built using Playwright and TypeScript to test the ItinereHub API endpoints. It includes integration with Zephyr Scale for test management and reporting.

## 📁 Project Structure

```
tests/api/
├── apiAutomation.spec.ts          # Main API automation test suite
├── customerManagement.spec.ts     # Customer management specific tests
├── travelerManagement.spec.ts     # Traveler management specific tests
├── apiTestRunner.spec.ts          # Comprehensive test runner with Zephyr integration
├── apiConfig.ts                   # Configuration and environment management
└── README.md                      # This documentation file
```

## 🚀 Features

### Core Functionality
- **Comprehensive API Testing**: Tests for Agency, Customer, Traveler, Trip, Accommodation, Transportation, and Flight management
- **Zephyr Scale Integration**: Automatic test cycle creation and result reporting
- **Response Validation**: Status codes, response times, content types, and custom validations
- **Environment Management**: Centralized configuration for different environments
- **Test Data Generation**: Dynamic test data creation with unique identifiers
- **Error Handling**: Robust error handling and detailed failure reporting

### Test Categories
1. **Agency Management**: Country lists, registration validation, agency creation
2. **Customer Management**: CRUD operations, search, status management, bulk operations
3. **Traveler Management**: Traveler lifecycle, passport/visa management, relationships
4. **Trip Management**: Trip creation, feedback, traveler assignments
5. **Accommodation**: Hotel and accommodation booking management
6. **Transportation**: Transport booking and management
7. **Flight Management**: Flight booking, leg details, traveler assignments

## 🛠️ Setup and Configuration

### Prerequisites
- Node.js (v16 or higher)
- Playwright installed and configured
- Valid API tokens for ItinereHub and Zephyr Scale

### Environment Configuration
Update the `apiConfig.ts` file with your environment-specific values:

```typescript
export const apiEnvironment: APIEnvironment = {
  // Update these URLs for your environment
  personUrl: 'https://your-person-service-url',
  urlCustomer: 'https://your-customer-service-url',
  urltraveler: 'https://your-traveler-service-url',
  
  // Update authentication tokens
  auth_token: 'Bearer your-jwt-token',
  zephyrApiToken: 'your-zephyr-api-token',
  
  // Update test data
  agentID: 'your-agent-id',
  customerId: your-customer-id,
  // ... other configuration
};
```

## 🧪 Running Tests

### Run All API Tests
```bash
# Run the comprehensive test suite
npx playwright test tests/api/apiTestRunner.spec.ts

# Run specific test files
npx playwright test tests/api/customerManagement.spec.ts
npx playwright test tests/api/travelerManagement.spec.ts
```

### Run Tests with Different Options
```bash
# Run tests in headed mode
npx playwright test tests/api/ --headed

# Run tests with specific reporter
npx playwright test tests/api/ --reporter=html

# Run tests in parallel
npx playwright test tests/api/ --workers=4

# Run specific test by name
npx playwright test tests/api/ --grep "Create Customer"
```

## 📊 Test Reporting

### Zephyr Scale Integration
The framework automatically:
1. Creates a test cycle in Zephyr Scale before test execution
2. Reports individual test results with status, comments, and response times
3. Links test executions to the appropriate test cases using test case keys

### Console Output
Tests provide detailed console output including:
- Test execution status (✅ Pass / ❌ Fail)
- Response times for each API call
- Detailed failure reasons
- Test execution summary with statistics

### Example Output
```
🧪 Executing: Get Countries List
✅ Get Countries List: PASSED (245ms)

🧪 Executing: Create Customer
✅ Create Customer: PASSED (1,234ms)

📈 TEST EXECUTION SUMMARY
========================
Total Tests: 15
Passed: 14 (93.3%)
Failed: 1 (6.7%)
Average Response Time: 892ms
Test Cycle: IHTC-C123
========================
```

## 🔧 Customization

### Adding New Test Cases
1. Add the test case key to `testCaseMapping` in `apiConfig.ts`
2. Create the test method in the appropriate test file
3. Use the `APITestRunner.executeAPITest()` method for consistent validation

Example:
```typescript
await apiRunner.executeAPITest({
  testName: 'Your Test Name',
  testCaseKey: testCaseMapping.YOUR_TEST_KEY,
  method: 'POST',
  url: `${apiEnvironment.baseUrl}/your-endpoint`,
  data: yourTestData,
  expectedStatus: 201,
  customValidations: [
    (response, body) => {
      expect(body).toHaveProperty('success', true);
      expect(body.data).toHaveProperty('id');
    }
  ]
});
```

### Custom Validations
Add custom validation functions to verify specific business logic:

```typescript
customValidations: [
  (response, body) => {
    // Validate response structure
    expect(body).toHaveProperty('success', true);
    
    // Validate data types
    expect(typeof body.data.id).toBe('number');
    
    // Validate business rules
    expect(body.data.status).toBe('ACTIVE');
    
    // Validate arrays
    expect(Array.isArray(body.data.items)).toBeTruthy();
    expect(body.data.items.length).toBeGreaterThan(0);
  }
]
```

## 🔍 Test Data Management

### Dynamic Test Data Generation
The framework includes utilities for generating unique test data:

```typescript
// Generate unique email
const email = APIUtils.generateUniqueEmail('customer');

// Generate unique string
const name = APIUtils.generateUniqueString('Test Customer');

// Generate random phone number
const phone = APIUtils.generateRandomPhoneNumber('+55');

// Format dates
const date = APIUtils.formatDate(new Date());
```

### Test Data Cleanup
Consider implementing cleanup procedures for test data:
- Delete created test records after test execution
- Use test-specific prefixes for easy identification
- Implement data archival for audit purposes

## 🚨 Troubleshooting

### Common Issues

1. **Authentication Failures**
   - Verify JWT token is valid and not expired
   - Check token format includes 'Bearer ' prefix
   - Ensure API permissions are correctly configured

2. **Network Timeouts**
   - Increase `responseTime` in `apiConfig.ts`
   - Check network connectivity to API endpoints
   - Verify API service availability

3. **Test Data Conflicts**
   - Ensure unique identifiers are used for test data
   - Check for existing data that might conflict
   - Implement proper test data cleanup

4. **Zephyr Integration Issues**
   - Verify Zephyr API token is valid
   - Check project key and test case keys are correct
   - Ensure Zephyr Scale permissions are properly configured

### Debug Mode
Enable debug logging by setting environment variables:
```bash
DEBUG=pw:api npx playwright test tests/api/
```

## 📈 Performance Monitoring

The framework tracks and reports:
- Individual API response times
- Average response times across test suite
- Response time thresholds and violations
- Performance trends over time

## 🔒 Security Considerations

- Store sensitive tokens in environment variables
- Use secure token rotation practices
- Implement proper access controls for test environments
- Regular security audits of test data and configurations

## 🤝 Contributing

When adding new tests or features:
1. Follow the existing code structure and naming conventions
2. Add appropriate error handling and logging
3. Include comprehensive validations
4. Update documentation and test case mappings
5. Test thoroughly before committing changes

## 📞 Support

For issues or questions:
- Check the troubleshooting section above
- Review Playwright documentation: https://playwright.dev/
- Check Zephyr Scale API documentation
- Contact the development team for environment-specific issues
