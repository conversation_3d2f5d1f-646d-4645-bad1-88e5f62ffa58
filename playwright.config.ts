import { defineConfig } from '@playwright/test';

export default defineConfig({
  testDir: './tests',
  timeout: 30000, // 30 seconds per test
  expect: {
    timeout: 5000, // 5 seconds for assertions
  },
  fullyParallel: true,
  forbidOnly: !!process.env.CI, // Fail if test.only is left in CI
  retries: process.env.CI ? 2 : 0, // Retry failed tests in CI
  workers: process.env.CI ? 1 : undefined, // Single worker in CI for stability
  reporter: [["line"], ["allure-playwright"]],

  use: {
    actionTimeout: 10000, // 10 seconds for actions
    navigationTimeout: 30000, // 30 seconds for navigation
    launchOptions: {
      args: process.env.CI
        ? ["--no-sandbox", "--disable-setuid-sandbox", "--disable-dev-shm-usage"]
        : ["--start-maximized"],
      headless: process.env.CI ? true : false, // Headless in CI, headed locally
    },
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    trace: 'retain-on-failure',

    /* Ignore HTTPS errors for API testing */
    ignoreHTTPSErrors: true,
  },

  /* Configure projects for multiple browsers */
  projects: [
    {
      name: 'chromium',
      use: {
        viewport: null,
      },
    },

    // {
    //   name: 'firefox',
    //   use: {
    //     viewport: null,
    //   },
    // },

    // {
    //   name: 'webkit',
    //   use: {
    //     viewport: null,
    //   },
    // },
  ],
});
