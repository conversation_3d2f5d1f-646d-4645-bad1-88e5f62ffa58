import { test, expect, APIRequestContext } from '@playwright/test';

// Traveler Management API Tests
interface TravelerData {
  firstName: string;
  lastName: string;
  email: string;
  contactNumber: string;
  dateOfBirth: string;
  gender: 'MALE' | 'FEMALE' | 'OTHER';
  countryId: number;
  customerId: number;
}

interface PassportData {
  passportNumber: string;
  issuingCountryId: number;
  issueDate: string;
  expiryDate: string;
  travelerId: number;
}

interface VisaData {
  visaNumber: string;
  countryId: number;
  issueDate: string;
  expiryDate: string;
  visaType: string;
  travelerId: number;
}

// Environment configuration
const env = {
  urltraveler: 'https://traveler-management-service-qa-775123275503.us-central1.run.app/traveler-management',
  auth_token: 'Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6ImZlNjVjY2I4ZWFkMGJhZWY1ZmQzNjE5NWQ2NTI4YTA1NGZiYjc2ZjMiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.RdIcOe-UIv8aJAewQPHOvUija2B5V1YUow5LSmRMZmZQZEVvP1-tC-iepy0u1fbyH7L0iiqgJdv5DVFKjsF1XFPZja3g0DrGP5smKkokai4hVp4pJHaPrlIJdle5GW2JGkGwcb5SfpMmQ4W91fXJRm1vz6LznL5LDGotnR5bJBF_Y_OP0qZxuQMpe9wOec823qHmcq5qexfywgUTT20tyW9cP0O2adLkEGWTRJwpovB2R_-hBmOWMu_rYUClYrNbVY04fA4qh2Yk7N1QlXycxY0Gpcci4tvcpbwKw7EkDpgAKeQTrDk9netoWIRiVlOJERR8asM5O1m3CTYFN8Zz6A',
  agentId: 221,
  customerId: 651, // From environment
  responseTime: 3000
};

class TravelerAPIHelper {
  private request: APIRequestContext;
  private createdTravelerId: number | null = null;
  private createdPassportId: number | null = null;
  private createdVisaId: number | null = null;

  constructor(request: APIRequestContext) {
    this.request = request;
  }

  async createTraveler(travelerData: TravelerData): Promise<number> {
    const response = await this.request.post(`${env.urltraveler}/api/v1/travelers`, {
      headers: {
        'Authorization': env.auth_token,
        'Content-Type': 'application/json'
      },
      data: travelerData
    });

    expect(response.ok()).toBeTruthy();
    const responseBody = await response.json();
    expect(responseBody.success).toBe(true);
    expect(responseBody.data).toHaveProperty('id');
    
    this.createdTravelerId = responseBody.data.id;
    return this.createdTravelerId;
  }

  async getTraveler(travelerId: number) {
    const response = await this.request.get(`${env.urltraveler}/api/v1/travelers/${travelerId}`, {
      headers: {
        'Authorization': env.auth_token
      }
    });

    expect(response.ok()).toBeTruthy();
    return await response.json();
  }

  async updateTraveler(travelerId: number, updateData: Partial<TravelerData>) {
    const response = await this.request.put(`${env.urltraveler}/api/v1/travelers/${travelerId}`, {
      headers: {
        'Authorization': env.auth_token,
        'Content-Type': 'application/json'
      },
      data: updateData
    });

    expect(response.ok()).toBeTruthy();
    return await response.json();
  }

  async searchTravelers(searchParams: { firstName?: string, lastName?: string, email?: string }) {
    const response = await this.request.post(`${env.urltraveler}/api/v1/travelers/search`, {
      headers: {
        'Authorization': env.auth_token,
        'Content-Type': 'application/json'
      },
      data: {
        ...searchParams,
        page: 0,
        size: 10
      }
    });

    expect(response.ok()).toBeTruthy();
    return await response.json();
  }

  async updateTravelerStatus(travelerId: number, status: 'ACTIVE' | 'INACTIVE') {
    const response = await this.request.patch(`${env.urltraveler}/api/v1/travelers/${travelerId}/status`, {
      headers: {
        'Authorization': env.auth_token,
        'Content-Type': 'application/json'
      },
      data: {
        status: status
      }
    });

    expect(response.ok()).toBeTruthy();
    return await response.json();
  }

  async addPassport(passportData: PassportData): Promise<number> {
    const response = await this.request.post(`${env.urltraveler}/api/v1/travelers/${passportData.travelerId}/passports`, {
      headers: {
        'Authorization': env.auth_token,
        'Content-Type': 'application/json'
      },
      data: passportData
    });

    expect(response.ok()).toBeTruthy();
    const responseBody = await response.json();
    expect(responseBody.success).toBe(true);
    
    this.createdPassportId = responseBody.data.id;
    return this.createdPassportId;
  }

  async getPassports(travelerId: number) {
    const response = await this.request.get(`${env.urltraveler}/api/v1/travelers/${travelerId}/passports`, {
      headers: {
        'Authorization': env.auth_token
      }
    });

    expect(response.ok()).toBeTruthy();
    return await response.json();
  }

  async addVisa(visaData: VisaData): Promise<number> {
    const response = await this.request.post(`${env.urltraveler}/api/v1/travelers/${visaData.travelerId}/visas`, {
      headers: {
        'Authorization': env.auth_token,
        'Content-Type': 'application/json'
      },
      data: visaData
    });

    expect(response.ok()).toBeTruthy();
    const responseBody = await response.json();
    expect(responseBody.success).toBe(true);
    
    this.createdVisaId = responseBody.data.id;
    return this.createdVisaId;
  }

  async getVisas(travelerId: number) {
    const response = await this.request.get(`${env.urltraveler}/api/v1/travelers/${travelerId}/visas`, {
      headers: {
        'Authorization': env.auth_token
      }
    });

    expect(response.ok()).toBeTruthy();
    return await response.json();
  }

  getCreatedTravelerId(): number | null {
    return this.createdTravelerId;
  }

  getCreatedPassportId(): number | null {
    return this.createdPassportId;
  }

  getCreatedVisaId(): number | null {
    return this.createdVisaId;
  }
}

test.describe('Traveler Management API Tests', () => {
  let travelerHelper: TravelerAPIHelper;
  let testTravelerData: TravelerData;

  test.beforeAll(async ({ request }) => {
    travelerHelper = new TravelerAPIHelper(request);
    
    // Generate unique test data
    const timestamp = Date.now();
    testTravelerData = {
      firstName: `TestFirst${timestamp}`,
      lastName: `TestLast${timestamp}`,
      email: `test.traveler.${timestamp}@example.com`,
      contactNumber: '+551130446571',
      dateOfBirth: '1990-01-01',
      gender: 'MALE',
      countryId: 30, // Brazil
      customerId: env.customerId
    };
  });

  test('TC-TM-01: Create Traveler Successfully', async ({ request }) => {
    const startTime = Date.now();
    
    const travelerId = await travelerHelper.createTraveler(testTravelerData);
    
    const responseTime = Date.now() - startTime;
    expect(responseTime).toBeLessThan(env.responseTime);
    expect(travelerId).toBeGreaterThan(0);
    
    console.log(`✅ Traveler created successfully with ID: ${travelerId}`);
  });

  test('TC-TM-02: Get Traveler by ID', async ({ request }) => {
    const travelerId = travelerHelper.getCreatedTravelerId();
    expect(travelerId).not.toBeNull();

    const startTime = Date.now();
    const travelerData = await travelerHelper.getTraveler(travelerId!);
    const responseTime = Date.now() - startTime;

    expect(responseTime).toBeLessThan(env.responseTime);
    expect(travelerData.success).toBe(true);
    expect(travelerData.data).toHaveProperty('id', travelerId);
    expect(travelerData.data).toHaveProperty('firstName', testTravelerData.firstName);
    expect(travelerData.data).toHaveProperty('lastName', testTravelerData.lastName);
    expect(travelerData.data).toHaveProperty('email', testTravelerData.email);
    
    console.log(`✅ Traveler retrieved successfully: ${travelerData.data.firstName} ${travelerData.data.lastName}`);
  });

  test('TC-TM-03: Update Traveler Information', async ({ request }) => {
    const travelerId = travelerHelper.getCreatedTravelerId();
    expect(travelerId).not.toBeNull();

    const updateData = {
      firstName: `Updated${testTravelerData.firstName}`,
      contactNumber: '+551130446572'
    };

    const startTime = Date.now();
    const updateResult = await travelerHelper.updateTraveler(travelerId!, updateData);
    const responseTime = Date.now() - startTime;

    expect(responseTime).toBeLessThan(env.responseTime);
    expect(updateResult.success).toBe(true);
    
    // Verify the update
    const updatedTraveler = await travelerHelper.getTraveler(travelerId!);
    expect(updatedTraveler.data.firstName).toBe(updateData.firstName);
    expect(updatedTraveler.data.contactNumber).toBe(updateData.contactNumber);
    
    console.log(`✅ Traveler updated successfully`);
  });

  test('TC-TM-04: Search Travelers', async ({ request }) => {
    const startTime = Date.now();
    const searchResults = await travelerHelper.searchTravelers({ 
      firstName: testTravelerData.firstName 
    });
    const responseTime = Date.now() - startTime;

    expect(responseTime).toBeLessThan(env.responseTime);
    expect(searchResults.success).toBe(true);
    expect(searchResults.data).toHaveProperty('content');
    expect(Array.isArray(searchResults.data.content)).toBeTruthy();
    
    console.log(`✅ Traveler search completed. Found ${searchResults.data.content.length} travelers`);
  });

  test('TC-TM-05: Add Passport to Traveler', async ({ request }) => {
    const travelerId = travelerHelper.getCreatedTravelerId();
    expect(travelerId).not.toBeNull();

    const passportData: PassportData = {
      passportNumber: `P${Date.now()}`,
      issuingCountryId: 30, // Brazil
      issueDate: '2020-01-01',
      expiryDate: '2030-01-01',
      travelerId: travelerId!
    };

    const startTime = Date.now();
    const passportId = await travelerHelper.addPassport(passportData);
    const responseTime = Date.now() - startTime;

    expect(responseTime).toBeLessThan(env.responseTime);
    expect(passportId).toBeGreaterThan(0);
    
    console.log(`✅ Passport added successfully with ID: ${passportId}`);
  });

  test('TC-TM-06: Get Traveler Passports', async ({ request }) => {
    const travelerId = travelerHelper.getCreatedTravelerId();
    expect(travelerId).not.toBeNull();

    const startTime = Date.now();
    const passportsData = await travelerHelper.getPassports(travelerId!);
    const responseTime = Date.now() - startTime;

    expect(responseTime).toBeLessThan(env.responseTime);
    expect(passportsData.success).toBe(true);
    expect(Array.isArray(passportsData.data)).toBeTruthy();
    expect(passportsData.data.length).toBeGreaterThan(0);
    
    console.log(`✅ Retrieved ${passportsData.data.length} passport(s) for traveler`);
  });

  test('TC-TM-07: Add Visa to Traveler', async ({ request }) => {
    const travelerId = travelerHelper.getCreatedTravelerId();
    expect(travelerId).not.toBeNull();

    const visaData: VisaData = {
      visaNumber: `V${Date.now()}`,
      countryId: 39, // Canada
      issueDate: '2023-01-01',
      expiryDate: '2025-01-01',
      visaType: 'TOURIST',
      travelerId: travelerId!
    };

    const startTime = Date.now();
    const visaId = await travelerHelper.addVisa(visaData);
    const responseTime = Date.now() - startTime;

    expect(responseTime).toBeLessThan(env.responseTime);
    expect(visaId).toBeGreaterThan(0);
    
    console.log(`✅ Visa added successfully with ID: ${visaId}`);
  });

  test('TC-TM-08: Update Traveler Status', async ({ request }) => {
    const travelerId = travelerHelper.getCreatedTravelerId();
    expect(travelerId).not.toBeNull();

    const startTime = Date.now();
    const updateResult = await travelerHelper.updateTravelerStatus(travelerId!, 'INACTIVE');
    const responseTime = Date.now() - startTime;

    expect(responseTime).toBeLessThan(env.responseTime);
    expect(updateResult.success).toBe(true);
    
    // Verify the status was updated
    const updatedTraveler = await travelerHelper.getTraveler(travelerId!);
    expect(updatedTraveler.data.status).toBe('INACTIVE');
    
    console.log(`✅ Traveler status updated to INACTIVE successfully`);
  });
});
